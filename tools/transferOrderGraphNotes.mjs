import { closeClient } from "./dbRootDashboard.mjs"
import { transferOneNote } from "./transferNotes/transferNotes.mjs"
import { importOne } from "./transferOrder/transfer.mjs"
import { getFolderNames } from "./xs/helper.mjs"

const orderRoot = "./tools/transferOrder/_Processing"
const statusRoot = "./tools/transferNotes/checklist_campaignStatus"

const folderNames = await getFolderNames(orderRoot)
if (folderNames.length < 0) {
	console.log("No order found in " + orderRoot)
	process.exit(1)
}

for (let folder of folderNames) {
	await removeOrderAndGraphAndNote(folder)
}

// for(let folder of folderNames){
// 	const orderId= await importOne(orderRoot, folder)
// 	await transferOneNote(orderId, statusRoot)
// }

//  await closeClient()



async function removeOrderAndGraphAndNote(folder){
	const orderPath= `${orderRoot}/${folder}/order.json`
	const order = await readJson(orderPath)
	if(!order) return
	const id = order.id
	
}