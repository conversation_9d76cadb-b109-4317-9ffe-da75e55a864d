{"idPrefix": "EM", "sort": "EM", "a/b testing": "no", "id": "EM000000574", "reseller": "2060 Digital DC", "client": "Carahsoft - IIG Smartsheet - June25", "email": "<EMAIL>", "name": "<PERSON> - WTOP News ", "billing address": "5425 Wisconsin Avenue, Suite 500 Chevy Chase, Maryland 20815", "physical address": " 11493 Sunset Hills Rd Suite 100 Reston, Virginia 20190", "seed": ["<EMAIL>"], "reporting recipient": "<EMAIL>", "lead list": "yes", "hot lead recipient": "", "slate id": "", "publication title": "", "head line": null, "message": null, "social media url": "", "display ad url": "", "audience type": "B2C and B2B", "geo target": "US (Nationwide)", "target filter": "Federal Technology Decision-Makers.", "need creative assistance": "no", "reflect live text": "yes", "template": "Template 2", "use exist creative": "", "provide body copy": "", "provide high quality images": "", "provide logos": "", "provide call to action": "", "provide direction": "", "clickthrough url": "https://federalnewsnetwork.com/innovation-in-government/2025/05/how-no-code-platforms-can-accelerate-improvements-to-citizen-services/?utm_medium=email&utm_id=2060digital&utm_source=email&utm_campaign=fnn_carahsoft_email_marketing", "notes": "", "additional notes": "Enhanced list of opens/clicks - We are looking for the following fields from the contact list: First name, last name Email address Agency (based on the domain we are targeting) Phone number, and Title.", "enable basic info": "yes", "enable slate": "no", "include email": "yes", "include XenonMail": "no", "include FaceMail": "no", "include Instagram": "no", "include InnoNative": "no", "include AMEX": "", "include ALC": "", "addon products": "no", "enable additional notes": "yes", "agree terms": "yes", "locker addon products": "yes", "locker basic info": "yes", "locker XenonMail": "", "locker FaceMail": "", "locker Instagram": "", "locker social info": "yes", "quantity": "17563", "start date": 1750737600000, "end date": 1751342400000, "from": "Federal News Network", "subject": "How can agencies solve IT modernization challenges?", "split B quantity": null, "split B start date": null, "split B end date": null, "split B from": null, "split B subject": null, "type of deployment": "Initial", "initial campaign id": "", "XenonMail start date": null, "XenonMail end date": null, "XenonMail impression": null, "FaceMail start date": null, "FaceMail end date": null, "FaceMail impression": null, "FaceMail package": "", "Instagram start date": null, "Instagram end date": null, "Instagram impression": null, "Instagram package": "", "create date": 1750771398603, "index date": "start date", "followup start date": 1750910400000, "split B followup start date": null, "followup end date": 1751515200000, "split B followup end date": null, "projectID": "202506241341595t7x", "split B audience type": null, "split B geo target": null, "split B target filter": null, "split B followup from": null, "split B followup subject": null, "XenonMail geo target": null, "FaceMail geo target": null, "Instagram geo target": null}