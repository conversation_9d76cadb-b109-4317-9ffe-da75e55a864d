{"idPrefix": "SEND", "sort": "SendLine", "a/b testing": "no", "id": "SEND000000147", "reseller": "Cumulus Detroit", "client": "GrossePointe Spine Center ", "email": "<EMAIL>", "name": "GrossePointe Spine Center ", "billing address": "3011 West Grand Blvd, Suite 800 Detroit, MI 48202", "physical address": "19519 Mack Ave, Grosse Pointe Woods, MI 48236", "seed": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "reporting recipient": "<EMAIL> ", "lead list": "yes", "hot lead recipient": "<EMAIL>", "slate id": "", "publication title": "", "head line": "N/A", "message": "N/A", "social media url": "", "display ad url": "", "audience type": "B2C", "geo target": "Within 30-miles of 19519 Mack Avenue, Grosse Pointe Woods, MI 48236", "target filter": "Age 35+\n\nHousehold Income $90,000+\n\nKey Words: Back, neck, spine, spinal adjustments, spinal treatment, spinal care, decompression, traction, injections, chiropractic, chiropractic care, chiropractic adjustment, back pain relief, chronic back pain, sciatica, herniated disc, disc degeneration, degenerative disc, rehabilitation exercises.", "need creative assistance": "no", "reflect live text": "no", "template": "Template 2", "use exist creative": "", "provide body copy": "", "provide high quality images": "", "provide logos": "", "provide call to action": "", "provide direction": "", "clickthrough url": "https://grossepointespinecenter.com", "notes": "", "additional notes": "", "enable basic info": "yes", "enable slate": "no", "include email": "yes", "include XenonMail": "yes", "include FaceMail": "yes", "include Instagram": "yes", "include InnoNative": "no", "include AMEX": "", "include ALC": "", "addon products": "no", "enable additional notes": "yes", "agree terms": "yes", "locker addon products": "yes", "locker basic info": "yes", "locker XenonMail": "", "locker FaceMail": "", "locker Instagram": "", "locker social info": "yes", "postal addresses": "no", "enable followup": "yes", "quantity": "20000", "start date": 1751947200000, "end date": 1752552000000, "from": "TBD", "subject": "TBD", "followup start date": 1752120000000, "followup end date": 1752724800000, "followup from": "TBD", "followup subject": "TBD", "split B quantity": null, "split B start date": null, "split B end date": null, "split B from": null, "split B subject": null, "split B followup start date": null, "split B followup end date": null, "split B followup from": null, "split B followup subject": null, "XenonMail start date": 1751947200000, "XenonMail end date": 1752552000000, "XenonMail impression": "", "FaceMail start date": 1751947200000, "FaceMail end date": 1752552000000, "FaceMail impression": "", "FaceMail package": "", "Instagram start date": 1751947200000, "Instagram end date": 1752552000000, "Instagram impression": "", "Instagram package": "", "create date": 1751035589749, "index date": "start date", "projectID": "20250627150451tZH4", "split B audience type": null, "split B geo target": null, "split B target filter": null}