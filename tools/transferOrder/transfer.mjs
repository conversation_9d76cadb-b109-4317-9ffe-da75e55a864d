
import { isDateProperty } from "./date.mjs";
import { isOrderTypeHasFollowup, isOrderTypeHasIntialProoflink } from "./followup.mjs";
import { getOrderProperties } from "./orderTypes/orderProperty.mjs";
import { getSortFromPrefix } from "./sort.mjs";
import { getFileNames, readJson, writeJson } from "../xs/helper.mjs";
import { extractEmail } from "./email.mjs";
import { getAllGraphTypes, getConvertedStatus, needConvertToNativeAds } from "./graph.mjs";
import { getStdStatus } from "./status.mjs";
import { saveToDB } from "./toDB.mjs";


async function importCampaign(projectFolder) {
	const order = await importOrder(projectFolder)
	const status = await importStatus(projectFolder)
	order.status = status
	const prooflink = await importProoflink(order.id, projectFolder)
	const graph = await importGraph(order.id, projectFolder)
	return { order, prooflink, graph }
}

async function importStatus(projectFolder) {
	const statusFilepath = `${projectFolder}/status.json`
	const status = await readJson(statusFilepath)
	if (!status) {
		return 'none'
	}
	const mappedName = getStdStatus(status)
	if (mappedName) {
		return mappedName
	}
	return 'none'
}



async function importGraph(id, projectFolder) {
	const idPrefix = getIdPrefixFromId(id)
	const allTypes = getAllGraphTypes()
	const graph = {}
	for (let type of allTypes) {
		let convertedType = type
		if (needConvertToNativeAds(idPrefix, type)) {
			convertedType = "NativeAds"
		}

		//report
		const reportPath = `${projectFolder}/${type}Report.json`
		const report = await readJson(reportPath)
		if (!report) continue
		delete report.error
		delete report.errors
		report.id = id
		report.type = convertedType
		report.active = "yes"
		if (!graph.allReports) graph.allReports = []
		graph.allReports.push(report)

		//status
		const graphStatusPath = `${projectFolder}/${type}Progress.json`
		const graphStatus = await readJson(graphStatusPath)
		if (graphStatus) {
			const convertedStatus = getConvertedStatus(graphStatus)
			const status = {
				id,
				type: convertedType,
				status: convertedStatus
			}

			if (!graph.allStatus) graph.allStatus = []
			graph.allStatus.push(status)
		}


		//detail
		const detailPath = `${projectFolder}/${type}Data.json`
		const detail = await readJson(detailPath)
		detail.id = id
		detail.type = convertedType
		if (!graph.allDetails) graph.allDetails = []
		graph.allDetails.push(detail)


		// running
		const runningFolder = `${projectFolder}/${type}Run`
		const t1BranchFolder = `${runningFolder}/t1`
		const t1files = await getFileNames(t1BranchFolder)
		if (t1files.length !== 0) {
			for (let file of t1files) {
				const index = parseInt(file.slice(0, file.lastIndexOf(".")))
				const fullPath = `${t1BranchFolder}/${file}`
				const data = await readJson(fullPath)
				data.id = id
				data.type = convertedType
				data.branch = "t1"
				data.file = index
				if (!graph.allRunning) graph.allRunning = []
				graph.allRunning.push(data)
			}
		}

		const t2BranchFolder = `${runningFolder}/t2`
		const t2files = await getFileNames(t2BranchFolder)
		if (t2files.length !== 0) {
			for (let file of t2files) {
				const index = parseInt(file.slice(0, file.lastIndexOf(".")))
				const fullPath = `${t2BranchFolder}/${file}`
				const data = await readJson(fullPath)
				data.id = id
				data.type = convertedType
				data.branch = "t2"
				data.file = index
				// console.log(data.id, data.type, data.branch,data.file)
				if (!graph.allRunning) graph.allRunning = []
				graph.allRunning.push(data)
			}
		}

		// refinedReport 
		const refinedReportPath = `${projectFolder}/${type}RefineReport.json`
		const refinedReport = await readJson(refinedReportPath)
		if (refinedReport) {
			refinedReport.id = id
			refinedReport.type = convertedType
			if (!graph.allRefinedReports) graph.allRefinedReports = []
			graph.allRefinedReports.push(refinedReport)
		}
	}
	return graph
}
async function importProoflink(id, projectFolder) {
	const idPrefix = getIdPrefixFromId(id)
	const prooflinkPath = `${projectFolder}/creative.json`
	let result = await readJson(prooflinkPath)
	if (!result) {
		result = {}
	}
	let prooflink = { id, fixed: {} }
	if (isOrderTypeHasIntialProoflink(idPrefix)) {
		if (!result.initial) {
			result.initial = ""
		}
		prooflink.fixed.initial = result.initial
	}
	if (isOrderTypeHasFollowup(idPrefix)) {
		if (!result.followup) {
			result.followup = ""
		}
		prooflink.fixed.followup = result.followup
	}
	return prooflink
}
async function importOrder(projectFolder) {
	const orderPath = `${projectFolder}/order.json`
	const statusPath = `${projectFolder}/status.json` //final status
	const status = await readJson(statusPath)
	const originOrder = await readJson(orderPath)
	const id = originOrder.id
	const idPrefix = getIdPrefixFromId(id)
	const order = {}
	const getPropertiesFun = getOrderProperties(idPrefix)
	const propertiesObj = getPropertiesFun()
	const properties = Object.keys(propertiesObj)
	properties.push("create date")
	for (let key of properties) {
		if (isDateProperty(key)) {
			if (originOrder[key] !== "") {
				let date = new Date(originOrder[key])
				let time = date.getTime()
				if (time == 0) {
					order[key] = null
				} else {
					order[key] = new Date(originOrder[key])
				}
			}
			else {
				order[key] = null
			}
		} else if (key === "seed") {
			const value = originOrder[key]
			if (typeof (value) === "string") {
				const array = extractEmail(value)
				order[key] = array
			} else if (Array.isArray(value)) {
				order[key] = value
			} else {
				order[key] = null
			}
		}
		else {
			order[key] = originOrder[key]
		}
	}
	order.status = status
	order.idPrefix = idPrefix
	order.sort = getSortFromPrefix(idPrefix)
	let createDate = order["create date"]
	if (createDate) {
		createDate = new Date(createDate)
	} else {
		createDate = new Date()
	}
	order["create date"] = createDate
	return order
}
function getIdPrefixFromId(id) {
	const idPrefix = id.replace(/[0-9]+/, '')
	if (idPrefix === "") return "EM"
	return idPrefix;
}


export async function importOne(root, projectFolderName) {
	// const root = "./query"
	// const projectFolderName = "20250303205716t4EJ"
	//EM000000487
	
	const projectFolder = `${root}/${projectFolderName}`

	const result = await importCampaign(projectFolder)
	if (result && result.error) {
		console.error(result)
		return
	}
	// await writeJson('./tools/transfer/test/output.json', result)
	const id = await saveToDB(result)
	console.log(`import ${id} complete!`)
	return id
}



