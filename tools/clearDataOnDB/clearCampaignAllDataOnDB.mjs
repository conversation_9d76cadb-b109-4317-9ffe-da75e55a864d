export async function clearCampaignCampaignOrdersOnDB(id) {
	try {
		const client = await getDBClient()
		const db = client.db('campaign')
		const collection = db.collection('orders')

		await collection.deleteMany({})
		return { ok: 'All campaign orders cleared successfully' }
	} catch (error) {
		const msg = 'Error clearing campaign orders'
		console.error(msg, error)
		throw { error: `${msg}, ${error.message}` }
	}
}
export async function clearCampaignCampaignHistoryOnDB(id) {
	try {
		const client = await getDBClient()
		const db = client.db('campaign')
		const collection = db.collection('history')

		await collection.deleteMany({})
		return { ok: 'All campaign history cleared successfully' }
	} catch (error) {
		const msg = 'Error clearing campaign history'
		console.error(msg, error)
		throw { error: `${msg}, ${error.message}` }
	}
}
export async function clearCampaignCampaignProofLinkOnDB(id) {
	try {
		const client = await getDBClient()
		const db = client.db('campaign')
		const collection = db.collection('prooflink')

		await collection.deleteMany({})
		return { ok: 'All campaign prooflink cleared successfully' }
	} catch (error) {
		const msg = 'Error clearing campaign prooflink'
		console.error(msg, error)
		throw { error: `${msg}, ${error.message}` }
	}
}

export async function clearCampaignGraphReportsOnDB(id) {
	try {
		const client = await getDBClient()
		const db = client.db('graph')
		const collection = db.collection('reports')

		await collection.deleteMany({})
		return { ok: 'All graph reports cleared successfully' }
	} catch (error) {
		const msg = 'Error clearing graph reports'
		console.error(msg, error)
		throw { error: `${msg}, ${error.message}` }
	}
}

export async function clearCampaignGraphDetailOnDB(id) {
	try {
		const client = await getDBClient()
		const db = client.db('graph')
		const collection = db.collection('details')

		await collection.deleteMany({})
		return { ok: 'All graph details cleared successfully' }
	} catch (error) {
		const msg = 'Error clearing graph details'
		console.error(msg, error)
		throw { error: `${msg}, ${error.message}` }
	}
}
export async function clearCampaignGraphRunningDataOnDB(id) {
	try {
		const client = await getDBClient()
		const db = client.db('graph')
		const collection = db.collection('running')

		await collection.deleteMany({})
		return { ok: 'All graph running data cleared successfully' }
	} catch (error) {
		const msg = 'Error clearing graph running data'
		console.error(msg, error)
		throw { error: `${msg}, ${error.message}` }
	}
}
export async function clearCampaignGraphStatusOnDB(id) {
	try {
		const client = await getDBClient()
		const db = client.db('graph')
		const collection = db.collection('status')

		await collection.deleteMany({})
		return { ok: 'All graph status cleared successfully' }
	} catch (error) {
		const msg = 'Error clearing graph status'
		console.error(msg, error)
		throw { error: `${msg}, ${error.message}` }
	}
}
export async function clearCampaignGraphRefinedReportsOnDB(id) {
	try {
		const client = await getDBClient()
		const db = client.db('graph')
		const collection = db.collection('refinedReports')

		await collection.deleteMany({})
		return { ok: 'All graph refined reports cleared successfully' }
	} catch (error) {
		const msg = 'Error clearing graph refined reports'
		console.error(msg, error)
		throw { error: `${msg}, ${error.message}` }
	}
}

export async function clearCampaignActionNotesOnDB(id) {
	try {
		const client = await getDBClient()
		const db = client.db('action')
		const collection = db.collection('notes')

		await collection.deleteMany({})
		return { ok: 'All action notes cleared successfully' }
	} catch (error) {
		const msg = 'Error clearing action notes '
		console.error(msg, error)
		throw { error: `${msg}, ${error.message}` }
	}
}

export async function clearCampaignActionSendOnDB(id) {
	try {
		const client = await getDBClient()
		const db = client.db('action')
		const collection = db.collection('send')

		await collection.deleteMany({})
		return { ok: 'All action send cleared successfully' }
	} catch (error) {
		const msg = 'Error clearing action send'
		console.error(msg, error)
		throw { error: `${msg}, ${error.message}` }
	}
}

export async function clearCampaignActionSavedInfoOnDB(id) {
	try {
		const client = await getDBClient()
		const db = client.db('action')
		const collection = db.collection('savedInfo')

		await collection.deleteMany({})
		return { ok: 'All action savedInfo cleared successfully' }
	} catch (error) {
		const msg = 'Error clearing action savedInfo'
		console.error(msg, error)
		throw { error: `${msg}, ${error.message}` }
	}
}